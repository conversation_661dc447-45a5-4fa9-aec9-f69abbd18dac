# 个人记账应用需求分析文档

## 1. 项目概述

### 1.1 项目名称

个人记账管理系统

### 1.2 项目描述

基于 Web 的个人记账应用，帮助用户记录和管理个人财务状况，提供直观的收支统计和分析功能。

### 1.3 技术栈

- **前端**: Nuxt.js
- **构建工具**: Vite
- **后端**: Express.js
- **数据库**: Supabase
- **目标平台**: Web 应用

## 2. 用户需求

### 2.1 目标用户

- 主要面向个人用户
- 需要管理个人财务的普通用户
- 希望了解个人消费习惯的用户

### 2.2 用户场景

- 日常收支记录
- 月度/年度财务回顾
- 多账户资金管理
- 消费分析和预算控制

## 3. 功能需求

### 3.1 用户管理模块

- **用户注册**
  - 邮箱注册
  - 手机号注册
  - 用户信息验证
- **用户登录**
  - 邮箱/手机号登录
  - 密码找回功能
  - 记住登录状态
- **用户信息管理**
  - 个人资料编辑
  - 密码修改
  - 账户安全设置

### 3.2 账户管理模块

- **多账户支持**
  - 银行卡账户
  - 支付宝账户
  - 微信支付账户
  - 现金账户
  - 其他自定义账户
- **账户操作**
  - 添加/删除账户
  - 编辑账户信息
  - 设置账户余额
  - 账户状态管理

### 3.3 收支记录模块

- **收入记录**
  - 收入金额输入
  - 收入来源分类（工资、投资、其他等）
  - 收入账户选择
  - 收入时间记录
  - 备注信息
- **支出记录**
  - 支出金额输入
  - 支出分类（餐饮、交通、购物、娱乐等）
  - 支出账户选择
  - 支出时间记录
  - 备注信息
- **记录管理**
  - 查看历史记录
  - 编辑/删除记录
  - 记录搜索和筛选

### 3.4 统计分析模块

- **月度统计**
  - 月收入统计
  - 月支出统计
  - 月结余计算
  - 分类支出占比
- **年度统计**
  - 年收入统计
  - 年支出统计
  - 年结余计算
  - 月度趋势对比
- **数据可视化**
  - 收支趋势图表
  - 分类支出饼图
  - 月度对比柱状图

## 4. 非功能需求

### 4.1 性能需求

- 页面加载时间 < 3 秒
- 数据查询响应时间 < 1 秒
- 支持并发用户数: 100+

### 4.2 安全需求

- 用户密码加密存储
- 数据传输 HTTPS 加密
- 用户会话管理
- 防止 SQL 注入攻击

### 4.3 兼容性需求

- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计，适配移动端
- 支持常见屏幕分辨率

### 4.4 可用性需求

- 界面简洁直观
- 操作流程简单
- 错误提示友好
- 支持键盘快捷操作

## 5. 数据库设计概要

### 5.1 主要数据表

- **users**: 用户信息表
- **accounts**: 账户信息表
- **categories**: 收支分类表
- **transactions**: 交易记录表
- **budgets**: 预算管理表（可选）

### 5.2 关键字段

- 用户 ID、账户 ID、分类 ID
- 交易金额、交易时间、交易类型
- 备注信息、创建时间、更新时间

## 6. 接口设计概要

### 6.1 用户相关接口

- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- GET /api/user/profile - 获取用户信息
- PUT /api/user/profile - 更新用户信息

### 6.2 账户相关接口

- GET /api/accounts - 获取账户列表
- POST /api/accounts - 创建账户
- PUT /api/accounts/:id - 更新账户
- DELETE /api/accounts/:id - 删除账户

### 6.3 交易相关接口

- GET /api/transactions - 获取交易记录
- POST /api/transactions - 创建交易记录
- PUT /api/transactions/:id - 更新交易记录
- DELETE /api/transactions/:id - 删除交易记录

### 6.4 统计相关接口

- GET /api/statistics/monthly - 月度统计
- GET /api/statistics/yearly - 年度统计
- GET /api/statistics/categories - 分类统计

## 7. 开发计划

### 7.1 开发阶段

1. **第一阶段**: 用户管理和基础架构搭建
2. **第二阶段**: 账户管理和收支记录功能
3. **第三阶段**: 统计分析和数据可视化
4. **第四阶段**: 优化和测试

### 7.2 预估工期

- 总开发周期: 4-6 周
- 测试和优化: 1-2 周

## 8. 风险评估

### 8.1 技术风险

- Supabase 服务稳定性
- 前后端接口对接复杂度
- 数据安全和隐私保护

### 8.2 业务风险

- 用户需求变更
- 竞品功能对比
- 用户体验优化需求

## 9. 后续扩展

### 9.1 可能的功能扩展

- 预算管理功能
- 数据导入导出
- 多币种支持
- 投资组合管理
- 家庭共享账本

### 9.2 技术扩展

- 移动端 App 开发
- 数据备份和恢复
- 第三方银行接口集成
- AI 智能分类推荐
