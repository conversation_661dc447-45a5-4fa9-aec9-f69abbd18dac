{"name": "record-backend", "version": "1.0.0", "description": "个人记账应用后端API服务", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "api", "accounting", "finance", "supabase"], "author": "", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.50.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.3", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.1.10"}}