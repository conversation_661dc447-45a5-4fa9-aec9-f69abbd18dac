import express from 'express';
import { body } from 'express-validator';
import { 
  register, 
  login, 
  logout, 
  refreshToken,
  forgotPassword,
  resetPassword 
} from '../controllers/authController.js';
import { validateRequest } from '../middleware/validation.js';

const router = express.Router();

// 用户注册
router.post('/register', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('姓名长度应在2-50个字符之间'),
  validateRequest
], register);

// 用户登录
router.post('/login', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空'),
  validateRequest
], login);

// 用户登出
router.post('/logout', logout);

// 刷新令牌
router.post('/refresh-token', refreshToken);

// 忘记密码
router.post('/forgot-password', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  validateRequest
], forgotPassword);

// 重置密码
router.post('/reset-password', [
  body('token')
    .notEmpty()
    .withMessage('重置令牌不能为空'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  validateRequest
], resetPassword);

export default router;
