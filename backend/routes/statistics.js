import express from "express";
import { query } from "express-validator";
import {
  getMonthlyStatistics,
  getYearlyStatistics,
  getCategoryStatistics,
  getUserStatistics,
  getIncomeExpenseComparison,
} from "../controllers/statisticsController.js";
import { authenticateToken } from "../middleware/auth.js";
import { validateRequest } from "../middleware/validation.js";

const router = express.Router();

// 所有统计路由都需要认证
router.use(authenticateToken);

// 获取月度统计
router.get(
  "/monthly",
  [
    query("year")
      .optional()
      .isInt({ min: 2000, max: 3000 })
      .withMessage("年份必须在2000-3000之间"),
    query("month")
      .optional()
      .isInt({ min: 1, max: 12 })
      .withMessage("月份必须在1-12之间"),
    validateRequest,
  ],
  getMonthlyStatistics
);

// 获取年度统计
router.get(
  "/yearly",
  [
    query("year")
      .optional()
      .isInt({ min: 2000, max: 3000 })
      .withMessage("年份必须在2000-3000之间"),
    validateRequest,
  ],
  getYearlyStatistics
);

// 获取分类统计
router.get(
  "/categories",
  [
    query("type")
      .optional()
      .isIn(["income", "expense", "all"])
      .withMessage("类型必须是income、expense或all"),
    query("start_date").optional().isISO8601().withMessage("开始日期格式无效"),
    query("end_date").optional().isISO8601().withMessage("结束日期格式无效"),
    validateRequest,
  ],
  getCategoryStatistics
);

// 获取用户统计
router.get(
  "/user",
  [
    query("start_date").optional().isISO8601().withMessage("开始日期格式无效"),
    query("end_date").optional().isISO8601().withMessage("结束日期格式无效"),
    validateRequest,
  ],
  getUserStatistics
);

// 获取收支对比
router.get(
  "/income-expense-comparison",
  [
    query("period")
      .optional()
      .isIn(["daily", "weekly", "monthly", "yearly"])
      .withMessage("周期必须是daily、weekly、monthly或yearly"),
    query("start_date").optional().isISO8601().withMessage("开始日期格式无效"),
    query("end_date").optional().isISO8601().withMessage("结束日期格式无效"),
    validateRequest,
  ],
  getIncomeExpenseComparison
);

// 获取趋势分析
router.get(
  "/trends",
  [
    query("period")
      .optional()
      .isIn(["monthly", "quarterly", "yearly"])
      .withMessage("周期必须是monthly、quarterly或yearly"),
    query("months")
      .optional()
      .isInt({ min: 1, max: 24 })
      .withMessage("月份数量必须在1-24之间"),
    validateRequest,
  ],
  getTrendAnalysis
);

export default router;
