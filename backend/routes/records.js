import express from 'express';
import { body, param, query } from 'express-validator';
import {
  getRecords,
  getRecordById,
  createRecord,
  updateRecord,
  deleteRecord,
  getRecordsByCategory,
  getRecordsByDateRange,
  getRecordStats
} from '../controllers/recordController.js';
import { authenticateToken } from '../middleware/auth.js';
import { validateRequest } from '../middleware/validation.js';

const router = express.Router();

// 所有记录路由都需要认证
router.use(authenticateToken);

// 获取用户所有记录
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('type')
    .optional()
    .isIn(['income', 'expense'])
    .withMessage('记录类型必须是income或expense'),
  query('category')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('分类名称不能为空'),
  validateRequest
], getRecords);

// 获取特定记录
router.get('/:id', [
  param('id').isUUID().withMessage('记录ID格式无效'),
  validateRequest
], getRecordById);

// 按分类获取记录
router.get('/category/:category', [
  param('category')
    .trim()
    .isLength({ min: 1 })
    .withMessage('分类名称不能为空'),
  validateRequest
], getRecordsByCategory);

// 按日期范围获取记录
router.get('/date-range/:start_date/:end_date', [
  param('start_date').isISO8601().withMessage('开始日期格式无效'),
  param('end_date').isISO8601().withMessage('结束日期格式无效'),
  validateRequest
], getRecordsByDateRange);

// 获取记录统计
router.get('/stats/summary', [
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('开始日期格式无效'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('结束日期格式无效'),
  validateRequest
], getRecordStats);

// 创建新记录
router.post('/', [
  body('type')
    .isIn(['income', 'expense'])
    .withMessage('记录类型必须是income或expense'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('金额必须大于0'),
  body('category')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('分类名称长度应在1-50个字符之间'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述长度不能超过500个字符'),
  body('record_date')
    .optional()
    .isISO8601()
    .withMessage('记录日期格式无效'),
  validateRequest
], createRecord);

// 更新记录
router.put('/:id', [
  param('id').isUUID().withMessage('记录ID格式无效'),
  body('type')
    .optional()
    .isIn(['income', 'expense'])
    .withMessage('记录类型必须是income或expense'),
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('金额必须大于0'),
  body('category')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('分类名称长度应在1-50个字符之间'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述长度不能超过500个字符'),
  body('record_date')
    .optional()
    .isISO8601()
    .withMessage('记录日期格式无效'),
  validateRequest
], updateRecord);

// 删除记录
router.delete('/:id', [
  param('id').isUUID().withMessage('记录ID格式无效'),
  validateRequest
], deleteRecord);

export default router;
