import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import dotenv from "dotenv";
import rateLimit from "express-rate-limit";

// 导入路由
import authRoutes from "./routes/auth.js";
import recordRoutes from "./routes/records.js";
import statisticsRoutes from "./routes/statistics.js";

// 导入中间件
import { errorHandler } from "./middleware/errorHandler.js";

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet());

// CORS 配置
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
  })
);

// 请求日志
app.use(morgan("combined"));

// 请求体解析
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 100, // 限制每个IP在窗口期内最多100个请求
  message: {
    error: "请求过于频繁，请稍后再试",
  },
});
app.use("/api/", limiter);

// 健康检查端点
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    message: "个人记账应用后端服务运行正常",
    timestamp: new Date().toISOString(),
  });
});

// API 路由
app.use("/api/auth", authRoutes);
app.use("/api/records", recordRoutes);
app.use("/api/statistics", statisticsRoutes);

// 404 处理
app.use("*", (req, res) => {
  res.status(404).json({
    error: "接口不存在",
    message: `路径 ${req.originalUrl} 未找到`,
  });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器运行在端口 ${PORT}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
  console.log(`🔗 API 基础路径: http://localhost:${PORT}/api`);
});

export default app;
