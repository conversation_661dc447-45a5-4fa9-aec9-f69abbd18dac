# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/

# nyc 测试覆盖率
.nyc_output

# 依赖锁定文件
package-lock.json
yarn.lock

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 微服务
.serverless/

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db
