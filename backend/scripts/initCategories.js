import CategoryModel from "../models/Category.js";

/**
 * 初始化系统分类脚本
 * 用于在系统首次部署时创建默认分类
 */
async function initializeCategories() {
  try {
    console.log("🚀 开始初始化系统分类...");

    const result = await CategoryModel.initializeSystemCategories();

    if (result.success) {
      if (result.existing_count) {
        console.log(
          `✅ 系统分类已存在 (${result.existing_count} 个)，无需重复初始化`
        );
      } else {
        console.log(
          `✅ 系统分类初始化成功，创建了 ${result.created_count} 个分类`
        );
        console.log("📋 创建的分类:");
        result.categories.forEach((category) => {
          console.log(`  ${category.icon} ${category.name} (${category.type})`);
        });
      }
    } else {
      console.error("❌ 系统分类初始化失败");
    }

    return result;
  } catch (error) {
    console.error("❌ 初始化系统分类时发生错误:", error);
    throw error;
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeCategories()
    .then(() => {
      console.log("🎉 分类初始化完成");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 分类初始化失败:", error);
      process.exit(1);
    });
}

export default initializeCategories;
