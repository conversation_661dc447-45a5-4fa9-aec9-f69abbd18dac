-- 个人记账应用数据库表结构
-- 适用于 Supabase PostgreSQL 数据库

-- 启用 UUID 扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- 分类表（全局共享）
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    type VARCHAR(10) NOT NULL CHECK (type IN ('income', 'expense')),
    description VARCHAR(200),
    icon VARCHAR(10),
    color VARCHAR(7),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(name, type)
);

-- 账单记录表
CREATE TABLE IF NOT EXISTS records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(10) NOT NULL CHECK (type IN ('income', 'expense')),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    category VARCHAR(50) NOT NULL,
    description TEXT,
    record_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 预算表
CREATE TABLE IF NOT EXISTS budgets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    period VARCHAR(20) NOT NULL CHECK (period IN ('monthly', 'quarterly', 'yearly')),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'exceeded')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_records_user_id ON records(user_id);
CREATE INDEX IF NOT EXISTS idx_records_type ON records(type);
CREATE INDEX IF NOT EXISTS idx_records_category ON records(category);
CREATE INDEX IF NOT EXISTS idx_records_date ON records(record_date);
CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id);
CREATE INDEX IF NOT EXISTS idx_budgets_category ON budgets(category);
CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets(start_date, end_date);

-- 创建更新时间自动更新的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为各表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_records_updated_at BEFORE UPDATE ON records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budgets_updated_at BEFORE UPDATE ON budgets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入系统默认分类数据的函数
CREATE OR REPLACE FUNCTION insert_system_categories()
RETURNS VOID AS $$
BEGIN
    -- 检查是否已经初始化过
    IF EXISTS (SELECT 1 FROM categories LIMIT 1) THEN
        RETURN;
    END IF;

    -- 插入默认收入分类
    INSERT INTO categories (name, type, description, icon, color, sort_order, is_active) VALUES
    ('工资', 'income', '工资收入', '💼', '#4CAF50', 1, true),
    ('奖金', 'income', '奖金收入', '🎁', '#FF9800', 2, true),
    ('投资收益', 'income', '投资理财收益', '📈', '#2196F3', 3, true),
    ('兼职', 'income', '兼职收入', '⚡', '#9C27B0', 4, true),
    ('礼金', 'income', '礼金收入', '🧧', '#F44336', 5, true),
    ('其他收入', 'income', '其他类型收入', '💰', '#607D8B', 6, true);

    -- 插入默认支出分类
    INSERT INTO categories (name, type, description, icon, color, sort_order, is_active) VALUES
    ('餐饮', 'expense', '餐饮消费', '🍽️', '#FF5722', 1, true),
    ('交通', 'expense', '交通出行费用', '🚗', '#3F51B5', 2, true),
    ('购物', 'expense', '购物消费', '🛍️', '#E91E63', 3, true),
    ('娱乐', 'expense', '娱乐休闲费用', '🎮', '#9C27B0', 4, true),
    ('医疗', 'expense', '医疗健康费用', '🏥', '#4CAF50', 5, true),
    ('教育', 'expense', '教育培训费用', '📚', '#FF9800', 6, true),
    ('住房', 'expense', '房租房贷等住房费用', '🏠', '#795548', 7, true),
    ('通讯', 'expense', '手机网络等通讯费用', '📱', '#607D8B', 8, true),
    ('水电', 'expense', '水电燃气费用', '💡', '#FFC107', 9, true),
    ('保险', 'expense', '各类保险费用', '🛡️', '#2196F3', 10, true),
    ('其他支出', 'expense', '其他类型支出', '💸', '#9E9E9E', 11, true);
END;
$$ LANGUAGE plpgsql;

-- 创建 RLS (Row Level Security) 策略
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE records ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的数据
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- 分类表允许所有认证用户查看（全局共享）
CREATE POLICY "All users can view categories" ON categories FOR SELECT USING (auth.uid() IS NOT NULL);
-- 只有管理员可以修改分类（这里暂时允许所有用户，后续可以添加管理员角色检查）
CREATE POLICY "Admins can manage categories" ON categories FOR ALL USING (auth.uid() IS NOT NULL);

-- 账单记录策略
CREATE POLICY "Users can view own records" ON records FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own records" ON records FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own records" ON records FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own records" ON records FOR DELETE USING (auth.uid() = user_id);

-- 预算策略
CREATE POLICY "Users can view own budgets" ON budgets FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own budgets" ON budgets FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own budgets" ON budgets FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own budgets" ON budgets FOR DELETE USING (auth.uid() = user_id);
