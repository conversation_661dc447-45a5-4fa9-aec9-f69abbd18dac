import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { supabaseAdmin } from "../config/database.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 数据库初始化脚本
 * 用于创建表结构和初始化数据
 */
export class DatabaseInitializer {
  constructor() {
    this.supabase = supabaseAdmin;
  }

  /**
   * 读取 SQL 文件
   * @param {string} filename - SQL 文件名
   * @returns {string} SQL 内容
   */
  readSQLFile(filename) {
    const filePath = path.join(__dirname, filename);
    return fs.readFileSync(filePath, "utf8");
  }

  /**
   * 执行 SQL 语句
   * @param {string} sql - SQL 语句
   * @returns {Promise<Object>} 执行结果
   */
  async executeSQL(sql) {
    try {
      const { data, error } = await this.supabase.rpc("exec_sql", {
        sql_query: sql,
      });

      if (error) {
        console.error("SQL 执行错误:", error);
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error("执行 SQL 失败:", error);
      throw error;
    }
  }

  /**
   * 检查表是否存在
   * @param {string} tableName - 表名
   * @returns {Promise<boolean>} 表是否存在
   */
  async tableExists(tableName) {
    try {
      const { data, error } = await this.supabase
        .from("information_schema.tables")
        .select("table_name")
        .eq("table_schema", "public")
        .eq("table_name", tableName)
        .single();

      return !error && !!data;
    } catch (error) {
      return false;
    }
  }

  /**
   * 创建数据库表结构
   * @returns {Promise<Object>} 创建结果
   */
  async createTables() {
    try {
      console.log("🔧 开始创建数据库表结构...");

      // 读取 schema.sql 文件
      const schemaSql = this.readSQLFile("schema.sql");

      // 分割 SQL 语句（按分号分割，但要处理函数定义中的分号）
      const statements = this.splitSQLStatements(schemaSql);

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await this.executeSQL(statement);
            successCount++;
          } catch (error) {
            errorCount++;
            errors.push({
              statement: statement.substring(0, 100) + "...",
              error: error.message,
            });
            console.error(
              "SQL 语句执行失败:",
              statement.substring(0, 100) + "..."
            );
            console.error("错误:", error.message);
          }
        }
      }

      console.log(
        `✅ 数据库表结构创建完成: ${successCount} 成功, ${errorCount} 失败`
      );

      return {
        success: errorCount === 0,
        successCount,
        errorCount,
        errors,
      };
    } catch (error) {
      console.error("❌ 创建数据库表结构失败:", error);
      throw error;
    }
  }

  /**
   * 分割 SQL 语句
   * @param {string} sql - SQL 内容
   * @returns {Array<string>} SQL 语句数组
   */
  splitSQLStatements(sql) {
    // 简单的 SQL 语句分割，处理函数定义
    const statements = [];
    let currentStatement = "";
    let inFunction = false;
    let dollarQuoteTag = null;

    const lines = sql.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();

      // 跳过注释行
      if (trimmedLine.startsWith("--") || trimmedLine === "") {
        continue;
      }

      // 检查是否进入或退出 dollar-quoted 字符串
      const dollarQuoteMatch = trimmedLine.match(/\$([^$]*)\$/);
      if (dollarQuoteMatch) {
        const tag = dollarQuoteMatch[1];
        if (!inFunction) {
          inFunction = true;
          dollarQuoteTag = tag;
        } else if (tag === dollarQuoteTag) {
          inFunction = false;
          dollarQuoteTag = null;
        }
      }

      currentStatement += line + "\n";

      // 如果不在函数定义中，且行以分号结尾，则认为是一个完整的语句
      if (!inFunction && trimmedLine.endsWith(";")) {
        statements.push(currentStatement.trim());
        currentStatement = "";
      }
    }

    // 添加最后一个语句（如果有）
    if (currentStatement.trim()) {
      statements.push(currentStatement.trim());
    }

    return statements;
  }

  /**
   * 验证数据库结构
   * @returns {Promise<Object>} 验证结果
   */
  async validateDatabase() {
    try {
      console.log("🔍 验证数据库结构...");

      const requiredTables = ["users", "categories", "records", "budgets"];
      const results = {};

      for (const tableName of requiredTables) {
        const exists = await this.tableExists(tableName);
        results[tableName] = exists;

        if (exists) {
          console.log(`✅ 表 ${tableName} 存在`);
        } else {
          console.log(`❌ 表 ${tableName} 不存在`);
        }
      }

      const allTablesExist = Object.values(results).every((exists) => exists);

      return {
        success: allTablesExist,
        tables: results,
      };
    } catch (error) {
      console.error("❌ 验证数据库结构失败:", error);
      throw error;
    }
  }

  /**
   * 初始化数据库
   * @returns {Promise<Object>} 初始化结果
   */
  async initialize() {
    try {
      console.log("🚀 开始初始化数据库...");

      // 1. 创建表结构
      const createResult = await this.createTables();

      // 2. 验证数据库结构
      const validateResult = await this.validateDatabase();

      if (validateResult.success) {
        console.log("🎉 数据库初始化成功！");
      } else {
        console.log("⚠️ 数据库初始化完成，但存在问题");
      }

      return {
        success: validateResult.success,
        createResult,
        validateResult,
      };
    } catch (error) {
      console.error("❌ 数据库初始化失败:", error);
      throw error;
    }
  }

  /**
   * 重置数据库（删除所有表）
   * @returns {Promise<Object>} 重置结果
   */
  async reset() {
    try {
      console.log("🗑️ 开始重置数据库...");

      const tables = [
        "budgets",
        "transactions",
        "categories",
        "accounts",
        "users",
      ];

      for (const tableName of tables) {
        try {
          await this.executeSQL(`DROP TABLE IF EXISTS ${tableName} CASCADE;`);
          console.log(`✅ 删除表 ${tableName}`);
        } catch (error) {
          console.error(`❌ 删除表 ${tableName} 失败:`, error.message);
        }
      }

      console.log("🎉 数据库重置完成！");

      return { success: true };
    } catch (error) {
      console.error("❌ 数据库重置失败:", error);
      throw error;
    }
  }
}

// 导出单例实例
export default new DatabaseInitializer();

// 如果直接运行此文件，则执行初始化
if (import.meta.url === `file://${process.argv[1]}`) {
  const initializer = new DatabaseInitializer();

  const command = process.argv[2];

  switch (command) {
    case "init":
      initializer.initialize().catch(console.error);
      break;
    case "reset":
      initializer.reset().catch(console.error);
      break;
    case "validate":
      initializer.validateDatabase().catch(console.error);
      break;
    default:
      console.log("使用方法:");
      console.log("  node init.js init     - 初始化数据库");
      console.log("  node init.js reset    - 重置数据库");
      console.log("  node init.js validate - 验证数据库结构");
  }
}
