import { supabase } from '../config/database.js';
import { AppError } from '../middleware/errorHandler.js';

// 获取月度统计
export const getMonthlyStatistics = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = req.query;

    const startDate = new Date(year, month - 1, 1).toISOString();
    const endDate = new Date(year, month, 0, 23, 59, 59).toISOString();

    // 获取月度记录数据
    const { data: records, error } = await supabase
      .from('records')
      .select(`
        type,
        amount,
        category,
        record_date
      `)
      .eq('user_id', userId)
      .gte('record_date', startDate)
      .lte('record_date', endDate);

    if (error) {
      console.error('获取月度统计错误:', error);
      throw new AppError('获取月度统计失败');
    }

    // 计算统计数据
    const stats = {
      total_income: 0,
      total_expense: 0,
      net_income: 0,
      record_count: records ? records.length : 0,
      income_by_category: {},
      expense_by_category: {},
      daily_summary: {}
    };

    if (records) {
      records.forEach(record => {
        const amount = parseFloat(record.amount);
        const date = new Date(record.record_date).toISOString().split('T')[0];

        if (record.type === 'income') {
          stats.total_income += amount;
          stats.income_by_category[record.category] = 
            (stats.income_by_category[record.category] || 0) + amount;
        } else {
          stats.total_expense += amount;
          stats.expense_by_category[record.category] = 
            (stats.expense_by_category[record.category] || 0) + amount;
        }

        // 日度汇总
        if (!stats.daily_summary[date]) {
          stats.daily_summary[date] = { income: 0, expense: 0 };
        }
        stats.daily_summary[date][record.type] += amount;
      });
    }

    stats.net_income = stats.total_income - stats.total_expense;

    res.json({
      success: true,
      message: '获取月度统计成功',
      data: {
        period: { year: parseInt(year), month: parseInt(month) },
        statistics: stats
      }
    });
  } catch (error) {
    next(error);
  }
};

// 获取年度统计
export const getYearlyStatistics = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { year = new Date().getFullYear() } = req.query;

    const startDate = new Date(year, 0, 1).toISOString();
    const endDate = new Date(year, 11, 31, 23, 59, 59).toISOString();

    // 获取年度记录数据
    const { data: records, error } = await supabase
      .from('records')
      .select(`
        type,
        amount,
        category,
        record_date
      `)
      .eq('user_id', userId)
      .gte('record_date', startDate)
      .lte('record_date', endDate);

    if (error) {
      console.error('获取年度统计错误:', error);
      throw new AppError('获取年度统计失败');
    }

    // 计算统计数据
    const stats = {
      total_income: 0,
      total_expense: 0,
      net_income: 0,
      record_count: records ? records.length : 0,
      monthly_summary: {},
      income_by_category: {},
      expense_by_category: {}
    };

    // 初始化月度汇总
    for (let month = 1; month <= 12; month++) {
      stats.monthly_summary[month] = { income: 0, expense: 0, net: 0 };
    }

    if (records) {
      records.forEach(record => {
        const amount = parseFloat(record.amount);
        const date = new Date(record.record_date);
        const month = date.getMonth() + 1;

        if (record.type === 'income') {
          stats.total_income += amount;
          stats.monthly_summary[month].income += amount;
          stats.income_by_category[record.category] = 
            (stats.income_by_category[record.category] || 0) + amount;
        } else {
          stats.total_expense += amount;
          stats.monthly_summary[month].expense += amount;
          stats.expense_by_category[record.category] = 
            (stats.expense_by_category[record.category] || 0) + amount;
        }
      });
    }

    // 计算每月净收入
    Object.keys(stats.monthly_summary).forEach(month => {
      const monthData = stats.monthly_summary[month];
      monthData.net = monthData.income - monthData.expense;
    });

    stats.net_income = stats.total_income - stats.total_expense;

    res.json({
      success: true,
      message: '获取年度统计成功',
      data: {
        period: { year: parseInt(year) },
        statistics: stats
      }
    });
  } catch (error) {
    next(error);
  }
};

// 获取分类统计
export const getCategoryStatistics = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { 
      type = 'all', 
      start_date, 
      end_date 
    } = req.query;

    // 构建查询
    let query = supabase
      .from('records')
      .select(`
        type,
        amount,
        category
      `)
      .eq('user_id', userId);

    if (type !== 'all') {
      query = query.eq('type', type);
    }
    if (start_date) {
      query = query.gte('record_date', start_date);
    }
    if (end_date) {
      query = query.lte('record_date', end_date);
    }

    const { data: records, error } = await query;

    if (error) {
      console.error('获取分类统计错误:', error);
      throw new AppError('获取分类统计失败');
    }

    // 计算分类统计
    const categoryStats = {};
    let totalAmount = 0;

    if (records) {
      records.forEach(record => {
        const amount = parseFloat(record.amount);
        const category = record.category;

        if (!categoryStats[category]) {
          categoryStats[category] = {
            category,
            total_amount: 0,
            record_count: 0,
            type: record.type
          };
        }

        categoryStats[category].total_amount += amount;
        categoryStats[category].record_count += 1;
        totalAmount += amount;
      });
    }

    // 计算百分比
    Object.values(categoryStats).forEach(stat => {
      stat.percentage = totalAmount > 0 ? (stat.total_amount / totalAmount * 100).toFixed(2) : 0;
    });

    res.json({
      success: true,
      message: '获取分类统计成功',
      data: {
        filter: { type, start_date, end_date },
        total_amount: totalAmount,
        categories: Object.values(categoryStats).sort((a, b) => b.total_amount - a.total_amount)
      }
    });
  } catch (error) {
    next(error);
  }
};

// 获取用户统计
export const getUserStatistics = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { start_date, end_date } = req.query;

    // 构建查询
    let query = supabase
      .from('records')
      .select('type, amount, record_date')
      .eq('user_id', userId);

    if (start_date) {
      query = query.gte('record_date', start_date);
    }
    if (end_date) {
      query = query.lte('record_date', end_date);
    }

    const { data: records, error } = await query;

    if (error) {
      console.error('获取用户统计错误:', error);
      throw new AppError('获取用户统计失败');
    }

    // 计算用户统计
    let totalIncome = 0;
    let totalExpense = 0;
    let recordCount = 0;

    if (records) {
      records.forEach(record => {
        const amount = parseFloat(record.amount);
        if (record.type === 'income') {
          totalIncome += amount;
        } else {
          totalExpense += amount;
        }
        recordCount++;
      });
    }

    res.json({
      success: true,
      message: '获取用户统计成功',
      data: {
        filter: { start_date, end_date },
        user_stats: {
          total_income: totalIncome,
          total_expense: totalExpense,
          net_income: totalIncome - totalExpense,
          record_count: recordCount
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// 获取收支对比
export const getIncomeExpenseComparison = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { 
      period = 'monthly', 
      start_date, 
      end_date 
    } = req.query;

    // 构建查询
    let query = supabase
      .from('records')
      .select(`
        type,
        amount,
        record_date
      `)
      .eq('user_id', userId);

    if (start_date) {
      query = query.gte('record_date', start_date);
    }
    if (end_date) {
      query = query.lte('record_date', end_date);
    }

    const { data: records, error } = await query.order('record_date');

    if (error) {
      console.error('获取收支对比错误:', error);
      throw new AppError('获取收支对比失败');
    }

    // 根据周期分组数据
    const comparison = {};

    if (records) {
      records.forEach(record => {
        const amount = parseFloat(record.amount);
        const date = new Date(record.record_date);
        let periodKey;

        switch (period) {
          case 'daily':
            periodKey = date.toISOString().split('T')[0];
            break;
          case 'weekly':
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            periodKey = weekStart.toISOString().split('T')[0];
            break;
          case 'monthly':
            periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case 'yearly':
            periodKey = date.getFullYear().toString();
            break;
          default:
            periodKey = date.toISOString().split('T')[0];
        }

        if (!comparison[periodKey]) {
          comparison[periodKey] = { income: 0, expense: 0, net: 0 };
        }

        comparison[periodKey][record.type] += amount;
        comparison[periodKey].net = comparison[periodKey].income - comparison[periodKey].expense;
      });
    }

    res.json({
      success: true,
      message: '获取收支对比成功',
      data: {
        period,
        filter: { start_date, end_date },
        comparison: Object.keys(comparison).sort().map(key => ({
          period: key,
          ...comparison[key]
        }))
      }
    });
  } catch (error) {
    next(error);
  }
};
