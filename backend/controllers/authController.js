import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { supabase, supabaseAdmin } from "../config/database.js";
import { generateToken } from "../middleware/auth.js";
import { config } from "../config/environment.js";
import {
  AppError,
  ValidationError,
  UnauthorizedError,
  ConflictError,
} from "../middleware/errorHandler.js";

// 用户注册
export const register = async (req, res, next) => {
  try {
    const { email, password, name } = req.body;

    // 检查用户是否已存在
    const { data: existingUser } = await supabase
      .from("users")
      .select("id")
      .eq("email", email)
      .single();

    if (existingUser) {
      throw new ConflictError("该邮箱已被注册");
    }

    // 加密密码
    const saltRounds = config.security.bcryptSaltRounds;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const { data: newUser, error } = await supabase
      .from("users")
      .insert([
        {
          email,
          password: hashedPassword,
          name,
          is_active: true,
          created_at: new Date().toISOString(),
        },
      ])
      .select("id, email, name, created_at")
      .single();

    if (error) {
      console.error("用户注册错误:", error);
      throw new AppError("注册失败，请稍后重试");
    }

    // 生成访问令牌
    const token = generateToken(newUser.id);

    res.status(201).json({
      success: true,
      message: "注册成功",
      data: {
        user: {
          id: newUser.id,
          email: newUser.email,
          name: newUser.name,
          created_at: newUser.created_at,
        },
        token,
      },
    });
  } catch (error) {
    next(error);
  }
};

// 用户登录
export const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // 查找用户
    const { data: user, error } = await supabase
      .from("users")
      .select("id, email, name, password, is_active")
      .eq("email", email)
      .single();

    if (error || !user) {
      throw new UnauthorizedError("邮箱或密码错误");
    }

    if (!user.is_active) {
      throw new UnauthorizedError("账户已被禁用，请联系管理员");
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedError("邮箱或密码错误");
    }

    // 更新最后登录时间
    await supabase
      .from("users")
      .update({ last_login_at: new Date().toISOString() })
      .eq("id", user.id);

    // 生成访问令牌
    const token = generateToken(user.id);

    res.json({
      success: true,
      message: "登录成功",
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
        },
        token,
      },
    });
  } catch (error) {
    next(error);
  }
};

// 用户登出
export const logout = async (req, res, next) => {
  try {
    // 在实际应用中，可以将令牌加入黑名单
    // 这里简单返回成功消息
    res.json({
      success: true,
      message: "登出成功",
    });
  } catch (error) {
    next(error);
  }
};

// 刷新令牌
export const refreshToken = async (req, res, next) => {
  try {
    const { token } = req.body;

    if (!token) {
      throw new ValidationError("缺少刷新令牌");
    }

    // 验证令牌并获取用户信息
    const decoded = jwt.verify(token, config.jwt.secret);

    const { data: user, error } = await supabase
      .from("users")
      .select("id, email, name, is_active")
      .eq("id", decoded.userId)
      .single();

    if (error || !user || !user.is_active) {
      throw new UnauthorizedError("无效的刷新令牌");
    }

    // 生成新的访问令牌
    const newToken = generateToken(user.id);

    res.json({
      success: true,
      message: "令牌刷新成功",
      data: {
        token: newToken,
      },
    });
  } catch (error) {
    next(error);
  }
};

// 忘记密码
export const forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    // 检查用户是否存在
    const { data: user } = await supabase
      .from("users")
      .select("id, email, name")
      .eq("email", email)
      .single();

    if (!user) {
      // 为了安全，即使用户不存在也返回成功消息
      return res.json({
        success: true,
        message: "如果该邮箱已注册，您将收到重置密码的邮件",
      });
    }

    // 生成重置令牌
    const resetToken = generateToken(user.id);

    // 在实际应用中，这里应该发送邮件
    // 现在只是返回令牌（仅用于开发测试）
    console.log(`密码重置令牌 (${email}):`, resetToken);

    res.json({
      success: true,
      message: "如果该邮箱已注册，您将收到重置密码的邮件",
      // 开发环境下返回令牌
      ...(config.nodeEnv === "development" && { resetToken }),
    });
  } catch (error) {
    next(error);
  }
};

// 重置密码
export const resetPassword = async (req, res, next) => {
  try {
    const { token, password } = req.body;

    // 验证重置令牌
    const decoded = jwt.verify(token, config.jwt.secret);

    // 加密新密码
    const saltRounds = config.security.bcryptSaltRounds;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 更新密码
    const { error } = await supabase
      .from("users")
      .update({
        password: hashedPassword,
        updated_at: new Date().toISOString(),
      })
      .eq("id", decoded.userId);

    if (error) {
      throw new AppError("密码重置失败，请稍后重试");
    }

    res.json({
      success: true,
      message: "密码重置成功，请使用新密码登录",
    });
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return next(new UnauthorizedError("无效或已过期的重置令牌"));
    }
    next(error);
  }
};
