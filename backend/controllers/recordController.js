import RecordModel from "../models/Record.js";
import {
  AppError,
  NotFoundError,
  ValidationError,
} from "../middleware/errorHandler.js";

// 获取用户所有记录
export const getRecords = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const filters = {
      type: req.query.type,
      category: req.query.category,
      start_date: req.query.start_date,
      end_date: req.query.end_date,
      min_amount: req.query.min_amount,
      max_amount: req.query.max_amount,
    };

    const pagination = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
    };

    const result = await RecordModel.getUserRecords(
      userId,
      filters,
      pagination
    );

    res.json({
      success: true,
      message: "获取记录列表成功",
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

// 获取特定记录
export const getRecordById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const record = await RecordModel.getRecordDetails(id, userId);

    res.json({
      success: true,
      message: "获取记录详情成功",
      data: { record },
    });
  } catch (error) {
    next(error);
  }
};

// 按分类获取记录
export const getRecordsByCategory = async (req, res, next) => {
  try {
    const { category } = req.params;
    const userId = req.user.id;

    const filters = { category };
    const result = await RecordModel.getUserRecords(userId, filters);

    res.json({
      success: true,
      message: "获取分类记录成功",
      data: {
        category,
        records: result.records,
        total: result.pagination.total,
      },
    });
  } catch (error) {
    next(error);
  }
};

// 按日期范围获取记录
export const getRecordsByDateRange = async (req, res, next) => {
  try {
    const { start_date: startDate, end_date: endDate } = req.params;
    const userId = req.user.id;

    const filters = {
      start_date: startDate,
      end_date: endDate,
    };
    const result = await RecordModel.getUserRecords(userId, filters);

    res.json({
      success: true,
      message: "获取日期范围记录成功",
      data: {
        date_range: { start: startDate, end: endDate },
        records: result.records,
        total: result.pagination.total,
      },
    });
  } catch (error) {
    next(error);
  }
};

// 获取记录统计
export const getRecordStats = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const filters = {
      start_date: req.query.start_date,
      end_date: req.query.end_date,
    };

    const stats = await RecordModel.getUserStats(userId, filters);

    res.json({
      success: true,
      message: "获取统计数据成功",
      data: {
        filter: filters,
        statistics: stats,
      },
    });
  } catch (error) {
    next(error);
  }
};

// 创建新记录
export const createRecord = async (req, res, next) => {
  try {
    const recordData = {
      ...req.body,
      user_id: req.user.id,
    };

    const newRecord = await RecordModel.createRecord(recordData);

    res.status(201).json({
      success: true,
      message: "记录创建成功",
      data: { record: newRecord },
    });
  } catch (error) {
    next(error);
  }
};

// 更新记录
export const updateRecord = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;

    const updatedRecord = await RecordModel.updateRecord(
      id,
      userId,
      updateData
    );

    res.json({
      success: true,
      message: "记录更新成功",
      data: { record: updatedRecord },
    });
  } catch (error) {
    next(error);
  }
};

// 删除记录
export const deleteRecord = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    await RecordModel.deleteRecord(id, userId);

    res.json({
      success: true,
      message: "记录删除成功",
    });
  } catch (error) {
    next(error);
  }
};
