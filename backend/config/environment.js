import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // 服务器配置
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // 前端配置
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  
  // Supabase 配置
  supabase: {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY
  },
  
  // JWT 配置
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback_secret_key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },
  
  // 数据库配置
  database: {
    url: process.env.DATABASE_URL
  },
  
  // 安全配置
  security: {
    rateLimitWindowMs: 15 * 60 * 1000, // 15 分钟
    rateLimitMax: 100, // 每个窗口期最大请求数
    bcryptSaltRounds: 12
  }
};

// 验证必要的环境变量
export const validateConfig = () => {
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'JWT_SECRET'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ 缺少必要的环境变量:', missing.join(', '));
    console.error('请检查 .env 文件或环境变量配置');
    return false;
  }
  
  console.log('✅ 环境变量配置验证通过');
  return true;
};

export default config;
