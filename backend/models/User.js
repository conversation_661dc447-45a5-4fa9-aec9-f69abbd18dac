import bcrypt from "bcryptjs";
import { BaseModel } from "./BaseModel.js";
import { config } from "../config/environment.js";
import {
  ValidationError,
  ConflictError,
  NotFoundError,
} from "../middleware/errorHandler.js";

/**
 * 用户模型类
 */
export class User extends BaseModel {
  constructor(data = {}) {
    super("users");

    // 用户属性定义
    this.id = data.id || null;
    this.email = data.email || null;
    this.password = data.password || null;
    this.name = data.name || null;
    this.is_active = data.is_active !== undefined ? data.is_active : true;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
    this.last_login_at = data.last_login_at || null;
  }

  /**
   * 获取用户属性列表
   * @returns {Array} 属性名称列表
   */
  static getAttributes() {
    return [
      "id",
      "email",
      "password",
      "name",
      "is_active",
      "created_at",
      "updated_at",
      "last_login_at",
    ];
  }

  /**
   * 获取公开属性（不包含敏感信息）
   * @returns {Array} 公开属性名称列表
   */
  static getPublicAttributes() {
    return [
      "id",
      "email",
      "name",
      "is_active",
      "created_at",
      "updated_at",
      "last_login_at",
    ];
  }

  /**
   * 转换为普通对象（不包含密码）
   * @returns {Object} 用户对象
   */
  toJSON() {
    const publicAttrs = User.getPublicAttributes();
    const result = {};

    publicAttrs.forEach((attr) => {
      if (this[attr] !== undefined) {
        result[attr] = this[attr];
      }
    });

    return result;
  }

  /**
   * 数据验证
   * @param {Object} data - 用户数据
   * @param {string} operation - 操作类型
   * @returns {Object} 验证结果
   */
  validate(data, operation = "create") {
    const errors = [];

    if (operation === "create") {
      // 邮箱验证
      if (!data.email) {
        errors.push("邮箱地址不能为空");
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        errors.push("邮箱地址格式无效");
      }

      // 密码验证
      if (!data.password) {
        errors.push("密码不能为空");
      } else if (data.password.length < 6) {
        errors.push("密码长度至少6位");
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(data.password)) {
        errors.push("密码必须包含大小写字母和数字");
      }

      // 姓名验证
      if (!data.name) {
        errors.push("姓名不能为空");
      } else if (data.name.length < 2 || data.name.length > 50) {
        errors.push("姓名长度应在2-50个字符之间");
      }
    }

    if (operation === "update") {
      // 更新时的验证
      if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        errors.push("邮箱地址格式无效");
      }
      if (data.name && (data.name.length < 2 || data.name.length > 50)) {
        errors.push("姓名长度应在2-50个字符之间");
      }
      if (data.password && data.password.length < 6) {
        errors.push("密码长度至少6位");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Object} 创建的用户信息
   */
  async createUser(userData) {
    // 数据验证
    const validation = this.validate(userData, "create");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 检查邮箱是否已存在
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new ConflictError("该邮箱已被注册");
    }

    // 加密密码
    const saltRounds = config.security.bcryptSaltRounds;
    const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

    // 创建用户
    const newUser = await this.create(
      {
        email: userData.email.toLowerCase(),
        password: hashedPassword,
        name: userData.name,
        is_active: true,
      },
      ["id", "email", "name", "is_active", "created_at"]
    );

    return newUser;
  }

  /**
   * 根据邮箱查找用户
   * @param {string} email - 邮箱地址
   * @returns {Object|null} 用户信息
   */
  async findByEmail(email) {
    const users = await this.findWhere({ email: email.toLowerCase() });
    return users.length > 0 ? users[0] : null;
  }

  /**
   * 验证用户密码
   * @param {string} email - 邮箱地址
   * @param {string} password - 密码
   * @returns {Object|null} 验证成功返回用户信息，失败返回null
   */
  async validatePassword(email, password) {
    const user = await this.findWhere({ email: email.toLowerCase() }, [
      "id",
      "email",
      "name",
      "password",
      "is_active",
    ]);

    if (user.length === 0) {
      return null;
    }

    const userData = user[0];
    const isPasswordValid = await bcrypt.compare(password, userData.password);

    if (!isPasswordValid) {
      return null;
    }

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = userData;
    return userWithoutPassword;
  }

  /**
   * 更新用户信息
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新后的用户信息
   */
  async updateUser(userId, updateData) {
    // 数据验证
    const validation = this.validate(updateData, "update");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 如果更新邮箱，检查是否已存在
    if (updateData.email) {
      const existingUser = await this.findByEmail(updateData.email);
      if (existingUser && existingUser.id !== userId) {
        throw new ConflictError("该邮箱已被其他用户使用");
      }
      updateData.email = updateData.email.toLowerCase();
    }

    // 如果更新密码，进行加密
    if (updateData.password) {
      const saltRounds = config.security.bcryptSaltRounds;
      updateData.password = await bcrypt.hash(updateData.password, saltRounds);
    }

    const updatedUser = await this.update(userId, updateData, [
      "id",
      "email",
      "name",
      "is_active",
      "updated_at",
    ]);

    return updatedUser;
  }

  /**
   * 更新最后登录时间
   * @param {string} userId - 用户ID
   * @returns {boolean} 更新是否成功
   */
  async updateLastLogin(userId) {
    try {
      await this.update(
        userId,
        {
          last_login_at: new Date().toISOString(),
        },
        ["id"]
      );
      return true;
    } catch (error) {
      console.error("更新最后登录时间失败:", error);
      return false;
    }
  }

  /**
   * 激活/禁用用户
   * @param {string} userId - 用户ID
   * @param {boolean} isActive - 是否激活
   * @returns {Object} 更新后的用户信息
   */
  async setUserStatus(userId, isActive) {
    const updatedUser = await this.update(userId, { is_active: isActive }, [
      "id",
      "email",
      "name",
      "is_active",
      "updated_at",
    ]);

    return updatedUser;
  }

  /**
   * 获取用户统计信息
   * @param {string} userId - 用户ID
   * @returns {Object} 用户统计信息
   */
  async getUserStats(userId) {
    try {
      // 获取用户基本信息
      const user = await this.findById(userId, [
        "id",
        "email",
        "name",
        "created_at",
        "last_login_at",
      ]);
      if (!user) {
        throw new NotFoundError("用户不存在");
      }

      // 获取账户数量
      const accountCount = await this.supabase
        .from("accounts")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId);

      // 获取交易记录数量
      const transactionCount = await this.supabase
        .from("transactions")
        .select("*", { count: "exact", head: true })
        .eq("accounts.user_id", userId);

      return {
        user,
        stats: {
          account_count: accountCount.count || 0,
          transaction_count: transactionCount.count || 0,
          member_since: user.created_at,
          last_login: user.last_login_at,
        },
      };
    } catch (error) {
      console.error("获取用户统计信息失败:", error);
      throw error;
    }
  }

  /**
   * 软删除用户（标记为非活跃）
   * @param {string} userId - 用户ID
   * @returns {boolean} 删除是否成功
   */
  async softDelete(userId) {
    try {
      await this.setUserStatus(userId, false);
      return true;
    } catch (error) {
      console.error("软删除用户失败:", error);
      return false;
    }
  }
}

// 导出单例实例
export default new User();
