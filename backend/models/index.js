/**
 * 模型统一导出文件
 * 提供所有数据模型的统一入口
 */

// 导入基础模型类
export { BaseModel } from "./BaseModel.js";

// 导入具体模型类
export { User } from "./User.js";
export { Category } from "./Category.js";
export { Record } from "./Record.js";
export { Budget } from "./Budget.js";

// 导入模型实例（单例）
import UserModel from "./User.js";
import CategoryModel from "./Category.js";
import RecordModel from "./Record.js";
import BudgetModel from "./Budget.js";

// 导出模型实例
export const Models = {
  User: UserModel,
  Category: CategoryModel,
  Record: RecordModel,
  Budget: BudgetModel,
};

// 默认导出
export default Models;

/**
 * 使用示例：
 *
 * // 方式1：导入单个模型类
 * import { User } from './models/index.js';
 * const userModel = new User();
 *
 * // 方式2：导入模型实例
 * import { Models } from './models/index.js';
 * const user = await Models.User.findById('123');
 *
 * // 方式3：导入所有模型
 * import Models from './models/index.js';
 * const user = await Models.User.findById('123');
 */
