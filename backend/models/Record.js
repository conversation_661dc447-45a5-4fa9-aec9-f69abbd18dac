import { BaseModel } from "./BaseModel.js";
import { ValidationError, NotFoundError } from "../middleware/errorHandler.js";

/**
 * 账单记录模型类
 */
export class Record extends BaseModel {
  constructor(data = {}) {
    super("records");

    // 账单记录属性定义
    this.id = data.id || null;
    this.user_id = data.user_id || null;
    this.type = data.type || null; // 'income' | 'expense'
    this.amount = data.amount || 0;
    this.category = data.category || null;
    this.description = data.description || "";
    this.record_date = data.record_date || null;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  /**
   * 获取账单记录属性列表
   * @returns {Array} 属性名称列表
   */
  static getAttributes() {
    return [
      "id",
      "user_id",
      "type",
      "amount",
      "category",
      "description",
      "record_date",
      "created_at",
      "updated_at",
    ];
  }

  /**
   * 转换为普通对象
   * @returns {Object} 账单记录对象
   */
  toJSON() {
    const attrs = Record.getAttributes();
    const result = {};

    attrs.forEach((attr) => {
      if (this[attr] !== undefined) {
        result[attr] = this[attr];
      }
    });

    return result;
  }

  // 记录类型
  static RECORD_TYPES = {
    INCOME: "income",
    EXPENSE: "expense",
  };

  /**
   * 数据验证
   * @param {Object} data - 记录数据
   * @param {string} operation - 操作类型
   * @returns {Object} 验证结果
   */
  validate(data, operation = "create") {
    const errors = [];

    if (operation === "create") {
      // 用户ID验证
      if (!data.user_id) {
        errors.push("用户ID不能为空");
      }

      // 记录类型验证
      if (!data.type) {
        errors.push("记录类型不能为空");
      } else if (!Object.values(Record.RECORD_TYPES).includes(data.type)) {
        errors.push("无效的记录类型");
      }

      // 金额验证
      if (!data.amount) {
        errors.push("金额不能为空");
      } else {
        const amount = parseFloat(data.amount);
        if (isNaN(amount) || amount <= 0) {
          errors.push("金额必须大于0");
        }
      }

      // 分类验证
      if (!data.category) {
        errors.push("分类不能为空");
      } else if (data.category.length > 50) {
        errors.push("分类名称长度不能超过50个字符");
      }
    }

    if (operation === "update") {
      // 更新时的验证
      if (
        data.type &&
        !Object.values(Record.RECORD_TYPES).includes(data.type)
      ) {
        errors.push("无效的记录类型");
      }
      if (data.amount !== undefined) {
        const amount = parseFloat(data.amount);
        if (isNaN(amount) || amount <= 0) {
          errors.push("金额必须大于0");
        }
      }
      if (data.category && data.category.length > 50) {
        errors.push("分类名称长度不能超过50个字符");
      }
    }

    // 描述长度验证
    if (data.description && data.description.length > 500) {
      errors.push("描述长度不能超过500个字符");
    }

    // 记录日期验证
    if (data.record_date) {
      const date = new Date(data.record_date);
      if (isNaN(date.getTime())) {
        errors.push("无效的记录日期格式");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 创建账单记录
   * @param {Object} recordData - 记录数据
   * @returns {Object} 创建的记录
   */
  async createRecord(recordData) {
    // 数据验证
    const validation = this.validate(recordData, "create");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 创建记录
    const newRecord = await this.create(
      {
        user_id: recordData.user_id,
        type: recordData.type,
        amount: parseFloat(recordData.amount),
        category: recordData.category,
        description: recordData.description || "",
        record_date: recordData.record_date || new Date().toISOString(),
      },
      [
        "id",
        "type",
        "amount",
        "category",
        "description",
        "record_date",
        "created_at",
      ]
    );

    return newRecord;
  }

  /**
   * 获取用户账单记录
   * @param {string} userId - 用户ID
   * @param {Object} filters - 筛选条件
   * @param {Object} pagination - 分页参数
   * @returns {Object} 记录列表和分页信息
   */
  async getUserRecords(userId, filters = {}, pagination = {}) {
    const { page = 1, limit = 20 } = pagination;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const conditions = { user_id: userId };

    // 添加筛选条件
    if (filters.type) {
      conditions.type = filters.type;
    }
    if (filters.category) {
      conditions.category = filters.category;
    }

    let queryOptions = {
      orderBy: { field: "record_date", ascending: false },
      limit,
      offset,
    };

    // 日期范围筛选需要特殊处理
    if (filters.start_date || filters.end_date) {
      let query = this.supabase
        .from("records")
        .select("*")
        .eq("user_id", userId);

      if (filters.type) query = query.eq("type", filters.type);
      if (filters.category) query = query.eq("category", filters.category);
      if (filters.start_date)
        query = query.gte("record_date", filters.start_date);
      if (filters.end_date) query = query.lte("record_date", filters.end_date);
      if (filters.min_amount)
        query = query.gte("amount", parseFloat(filters.min_amount));
      if (filters.max_amount)
        query = query.lte("amount", parseFloat(filters.max_amount));

      // 获取总数
      const { count, error: countError } = await query.select("*", {
        count: "exact",
        head: true,
      });
      if (countError) {
        console.error("获取记录总数错误:", countError);
        throw new Error("获取记录失败");
      }

      // 获取分页数据
      const { data: records, error } = await query
        .order("record_date", { ascending: false })
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error("获取记录错误:", error);
        throw new Error("获取记录失败");
      }

      return {
        records: records || [],
        pagination: {
          current_page: page,
          per_page: limit,
          total: count || 0,
          total_pages: Math.ceil((count || 0) / limit),
        },
      };
    }

    const records = await this.findWhere(
      conditions,
      Record.getAttributes(),
      queryOptions
    );

    const total = await this.count(conditions);

    return {
      records: records || [],
      pagination: {
        current_page: page,
        per_page: limit,
        total,
        total_pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 获取记录详情
   * @param {string} recordId - 记录ID
   * @param {string} userId - 用户ID
   * @returns {Object} 记录详情
   */
  async getRecordDetails(recordId, userId) {
    const record = await this.findById(recordId);

    if (!record || record.user_id !== userId) {
      throw new NotFoundError("记录不存在");
    }

    return record;
  }

  /**
   * 更新记录
   * @param {string} recordId - 记录ID
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新后的记录
   */
  async updateRecord(recordId, userId, updateData) {
    // 验证记录是否存在且属于当前用户
    await this.getRecordDetails(recordId, userId);

    // 数据验证
    const validation = this.validate(updateData, "update");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 处理金额更新
    if (updateData.amount !== undefined) {
      updateData.amount = parseFloat(updateData.amount);
    }

    const updatedRecord = await this.update(
      recordId,
      updateData,
      Record.getAttributes()
    );

    return updatedRecord;
  }

  /**
   * 删除记录
   * @param {string} recordId - 记录ID
   * @param {string} userId - 用户ID
   * @returns {boolean} 删除是否成功
   */
  async deleteRecord(recordId, userId) {
    // 验证记录是否存在且属于当前用户
    await this.getRecordDetails(recordId, userId);

    return await this.delete(recordId);
  }

  /**
   * 获取用户统计信息
   * @param {string} userId - 用户ID
   * @param {Object} filters - 筛选条件
   * @returns {Object} 统计信息
   */
  async getUserStats(userId, filters = {}) {
    const conditions = { user_id: userId };

    // 添加筛选条件
    if (filters.start_date || filters.end_date) {
      let query = this.supabase
        .from("records")
        .select("type, amount, category, record_date")
        .eq("user_id", userId);

      if (filters.start_date)
        query = query.gte("record_date", filters.start_date);
      if (filters.end_date) query = query.lte("record_date", filters.end_date);

      const { data: records, error } = await query;

      if (error) {
        console.error("获取统计数据错误:", error);
        throw new Error("获取统计失败");
      }

      return this.calculateStats(records || []);
    }

    const records = await this.findWhere(conditions, [
      "type",
      "amount",
      "category",
      "record_date",
    ]);
    return this.calculateStats(records);
  }

  /**
   * 计算统计数据
   * @param {Array} records - 记录列表
   * @returns {Object} 统计结果
   */
  calculateStats(records) {
    const stats = {
      total_income: 0,
      total_expense: 0,
      net_income: 0,
      record_count: records.length,
      income_count: 0,
      expense_count: 0,
      category_breakdown: {},
      monthly_breakdown: {},
    };

    records.forEach((record) => {
      const amount = parseFloat(record.amount);
      const category = record.category;
      const date = new Date(record.record_date);
      const monthKey = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}`;

      if (record.type === "income") {
        stats.total_income += amount;
        stats.income_count++;
      } else {
        stats.total_expense += amount;
        stats.expense_count++;
      }

      // 分类统计
      if (!stats.category_breakdown[category]) {
        stats.category_breakdown[category] = {
          income: 0,
          expense: 0,
          count: 0,
        };
      }
      stats.category_breakdown[category][record.type] += amount;
      stats.category_breakdown[category].count++;

      // 月度统计
      if (!stats.monthly_breakdown[monthKey]) {
        stats.monthly_breakdown[monthKey] = {
          income: 0,
          expense: 0,
          net: 0,
          count: 0,
        };
      }
      stats.monthly_breakdown[monthKey][record.type] += amount;
      stats.monthly_breakdown[monthKey].count++;
    });

    // 计算净收入和月度净收入
    stats.net_income = stats.total_income - stats.total_expense;
    Object.keys(stats.monthly_breakdown).forEach((month) => {
      const monthData = stats.monthly_breakdown[month];
      monthData.net = monthData.income - monthData.expense;
    });

    return stats;
  }
}

// 导出单例实例
export default new Record();
