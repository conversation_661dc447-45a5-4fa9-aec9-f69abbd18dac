import { supabase } from '../config/database.js';
import { 
  AppError, 
  NotFoundError, 
  ValidationError 
} from '../middleware/errorHandler.js';

/**
 * 基础模型类，提供通用的数据库操作方法
 */
export class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.supabase = supabase;
  }

  /**
   * 创建记录
   * @param {Object} data - 要创建的数据
   * @param {Array} selectFields - 要返回的字段
   * @returns {Object} 创建的记录
   */
  async create(data, selectFields = ['*']) {
    try {
      const { data: result, error } = await this.supabase
        .from(this.tableName)
        .insert([{
          ...data,
          created_at: new Date().toISOString()
        }])
        .select(selectFields.join(', '))
        .single();

      if (error) {
        console.error(`创建 ${this.tableName} 记录错误:`, error);
        throw new AppError(`创建记录失败: ${error.message}`);
      }

      return result;
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(`创建 ${this.tableName} 记录失败`);
    }
  }

  /**
   * 根据ID查找记录
   * @param {string} id - 记录ID
   * @param {Array} selectFields - 要返回的字段
   * @returns {Object|null} 找到的记录
   */
  async findById(id, selectFields = ['*']) {
    try {
      const { data, error } = await this.supabase
        .from(this.tableName)
        .select(selectFields.join(', '))
        .eq('id', id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error(`查找 ${this.tableName} 记录错误:`, error);
        throw new AppError(`查找记录失败: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(`查找 ${this.tableName} 记录失败`);
    }
  }

  /**
   * 根据条件查找记录
   * @param {Object} conditions - 查询条件
   * @param {Array} selectFields - 要返回的字段
   * @param {Object} options - 查询选项（排序、分页等）
   * @returns {Array} 找到的记录列表
   */
  async findWhere(conditions = {}, selectFields = ['*'], options = {}) {
    try {
      let query = this.supabase
        .from(this.tableName)
        .select(selectFields.join(', '));

      // 添加查询条件
      Object.entries(conditions).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else if (typeof value === 'object' && value !== null) {
          // 支持操作符查询，如 { age: { gte: 18 } }
          Object.entries(value).forEach(([operator, operatorValue]) => {
            query = query[operator](key, operatorValue);
          });
        } else {
          query = query.eq(key, value);
        }
      });

      // 添加排序
      if (options.orderBy) {
        const { field, ascending = true } = options.orderBy;
        query = query.order(field, { ascending });
      }

      // 添加分页
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error(`查询 ${this.tableName} 记录错误:`, error);
        throw new AppError(`查询记录失败: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(`查询 ${this.tableName} 记录失败`);
    }
  }

  /**
   * 更新记录
   * @param {string} id - 记录ID
   * @param {Object} data - 要更新的数据
   * @param {Array} selectFields - 要返回的字段
   * @returns {Object} 更新后的记录
   */
  async update(id, data, selectFields = ['*']) {
    try {
      const { data: result, error } = await this.supabase
        .from(this.tableName)
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select(selectFields.join(', '))
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw new NotFoundError(`${this.tableName} 记录不存在`);
        }
        console.error(`更新 ${this.tableName} 记录错误:`, error);
        throw new AppError(`更新记录失败: ${error.message}`);
      }

      return result;
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(`更新 ${this.tableName} 记录失败`);
    }
  }

  /**
   * 删除记录
   * @param {string} id - 记录ID
   * @returns {boolean} 删除是否成功
   */
  async delete(id) {
    try {
      const { error } = await this.supabase
        .from(this.tableName)
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`删除 ${this.tableName} 记录错误:`, error);
        throw new AppError(`删除记录失败: ${error.message}`);
      }

      return true;
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(`删除 ${this.tableName} 记录失败`);
    }
  }

  /**
   * 统计记录数量
   * @param {Object} conditions - 查询条件
   * @returns {number} 记录数量
   */
  async count(conditions = {}) {
    try {
      let query = this.supabase
        .from(this.tableName)
        .select('*', { count: 'exact', head: true });

      // 添加查询条件
      Object.entries(conditions).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      const { count, error } = await query;

      if (error) {
        console.error(`统计 ${this.tableName} 记录错误:`, error);
        throw new AppError(`统计记录失败: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(`统计 ${this.tableName} 记录失败`);
    }
  }

  /**
   * 检查记录是否存在
   * @param {string} id - 记录ID
   * @returns {boolean} 记录是否存在
   */
  async exists(id) {
    try {
      const record = await this.findById(id, ['id']);
      return !!record;
    } catch (error) {
      return false;
    }
  }

  /**
   * 批量创建记录
   * @param {Array} dataArray - 要创建的数据数组
   * @param {Array} selectFields - 要返回的字段
   * @returns {Array} 创建的记录列表
   */
  async createMany(dataArray, selectFields = ['*']) {
    try {
      const timestamp = new Date().toISOString();
      const dataWithTimestamp = dataArray.map(data => ({
        ...data,
        created_at: timestamp
      }));

      const { data: result, error } = await this.supabase
        .from(this.tableName)
        .insert(dataWithTimestamp)
        .select(selectFields.join(', '));

      if (error) {
        console.error(`批量创建 ${this.tableName} 记录错误:`, error);
        throw new AppError(`批量创建记录失败: ${error.message}`);
      }

      return result || [];
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(`批量创建 ${this.tableName} 记录失败`);
    }
  }

  /**
   * 数据验证方法（子类可重写）
   * @param {Object} data - 要验证的数据
   * @param {string} operation - 操作类型 ('create' | 'update')
   * @returns {Object} 验证结果
   */
  validate(data, operation = 'create') {
    // 基础验证逻辑，子类可以重写
    const errors = [];

    if (operation === 'create') {
      // 创建时的验证逻辑
    } else if (operation === 'update') {
      // 更新时的验证逻辑
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
