import { BaseModel } from "./BaseModel.js";
import { ValidationError, ConflictError } from "../middleware/errorHandler.js";

/**
 * 分类模型类
 */
export class Category extends BaseModel {
  constructor(data = {}) {
    super("categories");

    // 分类属性定义（全局共享，不关联用户）
    this.id = data.id || null;
    this.name = data.name || null;
    this.type = data.type || null;
    this.description = data.description || "";
    this.icon = data.icon || null;
    this.color = data.color || null;
    this.sort_order = data.sort_order || 0;
    this.is_active = data.is_active !== undefined ? data.is_active : true;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  /**
   * 获取分类属性列表
   * @returns {Array} 属性名称列表
   */
  static getAttributes() {
    return [
      "id",
      "name",
      "type",
      "description",
      "icon",
      "color",
      "sort_order",
      "is_active",
      "created_at",
      "updated_at",
    ];
  }

  /**
   * 转换为普通对象
   * @returns {Object} 分类对象
   */
  toJSON() {
    const attrs = Category.getAttributes();
    const result = {};

    attrs.forEach((attr) => {
      if (this[attr] !== undefined) {
        result[attr] = this[attr];
      }
    });

    return result;
  }

  // 分类类型
  static CATEGORY_TYPES = {
    INCOME: "income",
    EXPENSE: "expense",
  };

  // 默认收入分类
  static DEFAULT_INCOME_CATEGORIES = [
    {
      name: "工资",
      description: "工资收入",
      icon: "💼",
      color: "#4CAF50",
      sort_order: 1,
    },
    {
      name: "奖金",
      description: "奖金收入",
      icon: "🎁",
      color: "#FF9800",
      sort_order: 2,
    },
    {
      name: "投资收益",
      description: "投资理财收益",
      icon: "📈",
      color: "#2196F3",
      sort_order: 3,
    },
    {
      name: "兼职",
      description: "兼职收入",
      icon: "⚡",
      color: "#9C27B0",
      sort_order: 4,
    },
    {
      name: "礼金",
      description: "礼金收入",
      icon: "🧧",
      color: "#F44336",
      sort_order: 5,
    },
    {
      name: "其他收入",
      description: "其他类型收入",
      icon: "💰",
      color: "#607D8B",
      sort_order: 6,
    },
  ];

  // 默认支出分类
  static DEFAULT_EXPENSE_CATEGORIES = [
    {
      name: "餐饮",
      description: "餐饮消费",
      icon: "🍽️",
      color: "#FF5722",
      sort_order: 1,
    },
    {
      name: "交通",
      description: "交通出行费用",
      icon: "🚗",
      color: "#3F51B5",
      sort_order: 2,
    },
    {
      name: "购物",
      description: "购物消费",
      icon: "🛍️",
      color: "#E91E63",
      sort_order: 3,
    },
    {
      name: "娱乐",
      description: "娱乐休闲费用",
      icon: "🎮",
      color: "#9C27B0",
      sort_order: 4,
    },
    {
      name: "医疗",
      description: "医疗健康费用",
      icon: "🏥",
      color: "#4CAF50",
      sort_order: 5,
    },
    {
      name: "教育",
      description: "教育培训费用",
      icon: "📚",
      color: "#FF9800",
      sort_order: 6,
    },
    {
      name: "住房",
      description: "房租房贷等住房费用",
      icon: "🏠",
      color: "#795548",
      sort_order: 7,
    },
    {
      name: "通讯",
      description: "手机网络等通讯费用",
      icon: "📱",
      color: "#607D8B",
      sort_order: 8,
    },
    {
      name: "水电",
      description: "水电燃气费用",
      icon: "💡",
      color: "#FFC107",
      sort_order: 9,
    },
    {
      name: "保险",
      description: "各类保险费用",
      icon: "🛡️",
      color: "#2196F3",
      sort_order: 10,
    },
    {
      name: "其他支出",
      description: "其他类型支出",
      icon: "💸",
      color: "#9E9E9E",
      sort_order: 11,
    },
  ];

  /**
   * 数据验证
   * @param {Object} data - 分类数据
   * @param {string} operation - 操作类型
   * @returns {Object} 验证结果
   */
  validate(data, operation = "create") {
    const errors = [];

    if (operation === "create") {
      // 分类名称验证
      if (!data.name) {
        errors.push("分类名称不能为空");
      } else if (data.name.length < 1 || data.name.length > 50) {
        errors.push("分类名称长度应在1-50个字符之间");
      }

      // 分类类型验证
      if (!data.type) {
        errors.push("分类类型不能为空");
      } else if (!Object.values(Category.CATEGORY_TYPES).includes(data.type)) {
        errors.push("无效的分类类型");
      }
    }

    if (operation === "update") {
      // 更新时的验证
      if (data.name && (data.name.length < 1 || data.name.length > 50)) {
        errors.push("分类名称长度应在1-50个字符之间");
      }
      if (
        data.type &&
        !Object.values(Category.CATEGORY_TYPES).includes(data.type)
      ) {
        errors.push("无效的分类类型");
      }
    }

    // 描述长度验证
    if (data.description && data.description.length > 200) {
      errors.push("描述长度不能超过200个字符");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 初始化用户默认分类
   * @param {string} userId - 用户ID
   * @returns {Object} 创建结果
   */
  async initializeDefaultCategories(userId) {
    try {
      const defaultCategories = [];

      // 添加默认收入分类
      Category.DEFAULT_INCOME_CATEGORIES.forEach((category) => {
        defaultCategories.push({
          user_id: userId,
          name: category.name,
          type: Category.CATEGORY_TYPES.INCOME,
          description: category.description,
          is_default: true,
          is_active: true,
        });
      });

      // 添加默认支出分类
      Category.DEFAULT_EXPENSE_CATEGORIES.forEach((category) => {
        defaultCategories.push({
          user_id: userId,
          name: category.name,
          type: Category.CATEGORY_TYPES.EXPENSE,
          description: category.description,
          is_default: true,
          is_active: true,
        });
      });

      const createdCategories = await this.createMany(defaultCategories, [
        "id",
        "name",
        "type",
        "description",
        "is_default",
      ]);

      return {
        success: true,
        created_count: createdCategories.length,
        categories: createdCategories,
      };
    } catch (error) {
      console.error("初始化默认分类失败:", error);
      throw error;
    }
  }

  /**
   * 创建自定义分类
   * @param {Object} categoryData - 分类数据
   * @returns {Object} 创建的分类信息
   */
  async createCategory(categoryData) {
    // 数据验证
    const validation = this.validate(categoryData, "create");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 检查同一用户下同类型分类名称是否已存在
    const existingCategory = await this.findWhere({
      user_id: categoryData.user_id,
      name: categoryData.name,
      type: categoryData.type,
    });

    if (existingCategory.length > 0) {
      throw new ConflictError("该分类名称已存在");
    }

    // 创建分类
    const newCategory = await this.create(
      {
        user_id: categoryData.user_id,
        name: categoryData.name,
        type: categoryData.type,
        description: categoryData.description || "",
        is_default: false,
        is_active: true,
      },
      [
        "id",
        "name",
        "type",
        "description",
        "is_default",
        "is_active",
        "created_at",
      ]
    );

    return newCategory;
  }

  /**
   * 获取用户所有分类
   * @param {string} userId - 用户ID
   * @param {string} type - 分类类型（可选）
   * @param {boolean} activeOnly - 是否只获取活跃分类
   * @returns {Array} 分类列表
   */
  async getUserCategories(userId, type = null, activeOnly = true) {
    const conditions = { user_id: userId };

    if (type) {
      conditions.type = type;
    }

    if (activeOnly) {
      conditions.is_active = true;
    }

    const categories = await this.findWhere(
      conditions,
      [
        "id",
        "name",
        "type",
        "description",
        "is_default",
        "is_active",
        "created_at",
      ],
      { orderBy: { field: "name", ascending: true } }
    );

    return categories;
  }

  /**
   * 获取分类统计信息
   * @param {string} userId - 用户ID
   * @param {string} categoryId - 分类ID
   * @returns {Object} 分类统计信息
   */
  async getCategoryStats(userId, categoryId) {
    // 验证分类是否属于用户
    const category = await this.findById(categoryId);
    if (!category || category.user_id !== userId) {
      throw new NotFoundError("分类不存在");
    }

    // 获取该分类的交易统计
    const { data: transactions, error } = await this.supabase
      .from("transactions")
      .select(
        `
        amount,
        transaction_date,
        accounts!inner(user_id)
      `
      )
      .eq("category", category.name)
      .eq("accounts.user_id", userId);

    if (error) {
      console.error("获取分类统计错误:", error);
      throw new Error("获取分类统计失败");
    }

    let totalAmount = 0;
    let transactionCount = 0;
    const monthlyStats = {};

    if (transactions) {
      transactions.forEach((transaction) => {
        const amount = parseFloat(transaction.amount);
        totalAmount += amount;
        transactionCount++;

        // 按月统计
        const date = new Date(transaction.transaction_date);
        const monthKey = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}`;

        if (!monthlyStats[monthKey]) {
          monthlyStats[monthKey] = { amount: 0, count: 0 };
        }
        monthlyStats[monthKey].amount += amount;
        monthlyStats[monthKey].count++;
      });
    }

    return {
      category,
      stats: {
        total_amount: totalAmount,
        transaction_count: transactionCount,
        average_amount:
          transactionCount > 0 ? totalAmount / transactionCount : 0,
        monthly_breakdown: monthlyStats,
      },
    };
  }

  /**
   * 更新分类信息
   * @param {string} categoryId - 分类ID
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新后的分类信息
   */
  async updateCategory(categoryId, userId, updateData) {
    // 验证分类是否存在且属于当前用户
    const existingCategory = await this.findById(categoryId);
    if (!existingCategory || existingCategory.user_id !== userId) {
      throw new NotFoundError("分类不存在");
    }

    // 不允许修改默认分类的核心属性
    if (existingCategory.is_default) {
      const restrictedFields = ["name", "type"];
      const hasRestrictedUpdate = restrictedFields.some((field) =>
        updateData.hasOwnProperty(field)
      );
      if (hasRestrictedUpdate) {
        throw new ValidationError("不能修改默认分类的名称和类型");
      }
    }

    // 数据验证
    const validation = this.validate(updateData, "update");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 如果更新名称，检查是否与其他分类重名
    if (updateData.name && updateData.name !== existingCategory.name) {
      const conflictCategory = await this.findWhere({
        user_id: userId,
        name: updateData.name,
        type: updateData.type || existingCategory.type,
      });

      if (
        conflictCategory.length > 0 &&
        conflictCategory[0].id !== categoryId
      ) {
        throw new ConflictError("该分类名称已存在");
      }
    }

    const updatedCategory = await this.update(categoryId, updateData, [
      "id",
      "name",
      "type",
      "description",
      "is_default",
      "is_active",
      "updated_at",
    ]);

    return updatedCategory;
  }

  /**
   * 删除分类
   * @param {string} categoryId - 分类ID
   * @param {string} userId - 用户ID
   * @returns {boolean} 删除是否成功
   */
  async deleteCategory(categoryId, userId) {
    // 验证分类是否存在且属于当前用户
    const category = await this.findById(categoryId);
    if (!category || category.user_id !== userId) {
      throw new NotFoundError("分类不存在");
    }

    // 不允许删除默认分类
    if (category.is_default) {
      throw new ValidationError("不能删除默认分类");
    }

    // 检查是否有关联的交易记录
    const { data: transactions, error } = await this.supabase
      .from("transactions")
      .select(
        `
        id,
        accounts!inner(user_id)
      `
      )
      .eq("category", category.name)
      .eq("accounts.user_id", userId)
      .limit(1);

    if (error) {
      console.error("检查交易记录错误:", error);
      throw new Error("删除分类失败");
    }

    if (transactions && transactions.length > 0) {
      throw new ValidationError(
        "无法删除有交易记录的分类，请先删除或修改相关交易记录"
      );
    }

    return await this.delete(categoryId);
  }

  /**
   * 激活/禁用分类
   * @param {string} categoryId - 分类ID
   * @param {string} userId - 用户ID
   * @param {boolean} isActive - 是否激活
   * @returns {Object} 更新后的分类信息
   */
  async setCategoryStatus(categoryId, userId, isActive) {
    return await this.updateCategory(categoryId, userId, {
      is_active: isActive,
    });
  }

  /**
   * 搜索分类
   * @param {string} userId - 用户ID
   * @param {string} keyword - 搜索关键词
   * @param {string} type - 分类类型（可选）
   * @returns {Array} 匹配的分类列表
   */
  async searchCategories(userId, keyword, type = null) {
    let query = this.supabase
      .from("categories")
      .select("id, name, type, description, is_default, is_active")
      .eq("user_id", userId)
      .or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`);

    if (type) {
      query = query.eq("type", type);
    }

    const { data: categories, error } = await query.order("name");

    if (error) {
      console.error("搜索分类错误:", error);
      throw new Error("搜索分类失败");
    }

    return categories || [];
  }
}

// 导出单例实例
export default new Category();
