import { BaseModel } from "./BaseModel.js";
import {
  ValidationError,
  ConflictError,
  NotFoundError,
} from "../middleware/errorHandler.js";

/**
 * 预算模型类
 */
export class Budget extends BaseModel {
  constructor(data = {}) {
    super("budgets");

    // 预算属性定义
    this.id = data.id || null;
    this.user_id = data.user_id || null;
    this.category = data.category || null;
    this.amount = data.amount || 0;
    this.period = data.period || null;
    this.start_date = data.start_date || null;
    this.end_date = data.end_date || null;
    this.description = data.description || "";
    this.status = data.status || "active";
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  /**
   * 获取预算属性列表
   * @returns {Array} 属性名称列表
   */
  static getAttributes() {
    return [
      "id",
      "user_id",
      "category",
      "amount",
      "period",
      "start_date",
      "end_date",
      "description",
      "status",
      "created_at",
      "updated_at",
    ];
  }

  /**
   * 转换为普通对象
   * @returns {Object} 预算对象
   */
  toJSON() {
    const attrs = Budget.getAttributes();
    const result = {};

    attrs.forEach((attr) => {
      if (this[attr] !== undefined) {
        result[attr] = this[attr];
      }
    });

    return result;
  }

  // 预算周期类型
  static PERIOD_TYPES = {
    MONTHLY: "monthly",
    QUARTERLY: "quarterly",
    YEARLY: "yearly",
  };

  // 预算状态
  static BUDGET_STATUS = {
    ACTIVE: "active",
    INACTIVE: "inactive",
    EXCEEDED: "exceeded",
  };

  /**
   * 数据验证
   * @param {Object} data - 预算数据
   * @param {string} operation - 操作类型
   * @returns {Object} 验证结果
   */
  validate(data, operation = "create") {
    const errors = [];

    if (operation === "create") {
      // 用户ID验证
      if (!data.user_id) {
        errors.push("用户ID不能为空");
      }

      // 分类验证
      if (!data.category) {
        errors.push("预算分类不能为空");
      } else if (data.category.length > 50) {
        errors.push("分类名称长度不能超过50个字符");
      }

      // 预算金额验证
      if (!data.amount) {
        errors.push("预算金额不能为空");
      } else {
        const amount = parseFloat(data.amount);
        if (isNaN(amount) || amount <= 0) {
          errors.push("预算金额必须大于0");
        }
      }

      // 预算周期验证
      if (!data.period) {
        errors.push("预算周期不能为空");
      } else if (!Object.values(Budget.PERIOD_TYPES).includes(data.period)) {
        errors.push("无效的预算周期类型");
      }

      // 开始日期验证
      if (!data.start_date) {
        errors.push("开始日期不能为空");
      } else {
        const startDate = new Date(data.start_date);
        if (isNaN(startDate.getTime())) {
          errors.push("无效的开始日期格式");
        }
      }
    }

    if (operation === "update") {
      // 更新时的验证
      if (data.category && data.category.length > 50) {
        errors.push("分类名称长度不能超过50个字符");
      }
      if (data.amount !== undefined) {
        const amount = parseFloat(data.amount);
        if (isNaN(amount) || amount <= 0) {
          errors.push("预算金额必须大于0");
        }
      }
      if (
        data.period &&
        !Object.values(Budget.PERIOD_TYPES).includes(data.period)
      ) {
        errors.push("无效的预算周期类型");
      }
      if (data.start_date) {
        const startDate = new Date(data.start_date);
        if (isNaN(startDate.getTime())) {
          errors.push("无效的开始日期格式");
        }
      }
    }

    // 描述长度验证
    if (data.description && data.description.length > 500) {
      errors.push("描述长度不能超过500个字符");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 计算预算结束日期
   * @param {string} startDate - 开始日期
   * @param {string} period - 预算周期
   * @returns {string} 结束日期
   */
  calculateEndDate(startDate, period) {
    const start = new Date(startDate);
    let end = new Date(start);

    switch (period) {
      case Budget.PERIOD_TYPES.MONTHLY:
        end.setMonth(start.getMonth() + 1);
        end.setDate(end.getDate() - 1);
        break;
      case Budget.PERIOD_TYPES.QUARTERLY:
        end.setMonth(start.getMonth() + 3);
        end.setDate(end.getDate() - 1);
        break;
      case Budget.PERIOD_TYPES.YEARLY:
        end.setFullYear(start.getFullYear() + 1);
        end.setDate(end.getDate() - 1);
        break;
      default:
        throw new Error("无效的预算周期");
    }

    return end.toISOString();
  }

  /**
   * 创建预算
   * @param {Object} budgetData - 预算数据
   * @returns {Object} 创建的预算信息
   */
  async createBudget(budgetData) {
    // 数据验证
    const validation = this.validate(budgetData, "create");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 计算结束日期
    const endDate = this.calculateEndDate(
      budgetData.start_date,
      budgetData.period
    );

    // 检查是否已存在相同分类和时间段的预算
    const { data: existingBudgets, error: checkError } = await this.supabase
      .from("budgets")
      .select("id")
      .eq("user_id", budgetData.user_id)
      .eq("category", budgetData.category)
      .or(`start_date.lte.${endDate},end_date.gte.${budgetData.start_date}`);

    if (checkError) {
      console.error("检查预算冲突错误:", checkError);
      throw new Error("创建预算失败");
    }

    if (existingBudgets && existingBudgets.length > 0) {
      throw new ConflictError("该分类在指定时间段内已存在预算");
    }

    // 创建预算
    const newBudget = await this.create(
      {
        user_id: budgetData.user_id,
        category: budgetData.category,
        amount: parseFloat(budgetData.amount),
        period: budgetData.period,
        start_date: budgetData.start_date,
        end_date: endDate,
        description: budgetData.description || "",
        status: Budget.BUDGET_STATUS.ACTIVE,
      },
      [
        "id",
        "category",
        "amount",
        "period",
        "start_date",
        "end_date",
        "description",
        "status",
        "created_at",
      ]
    );

    return newBudget;
  }

  /**
   * 获取用户所有预算
   * @param {string} userId - 用户ID
   * @param {Object} filters - 筛选条件
   * @returns {Array} 预算列表
   */
  async getUserBudgets(userId, filters = {}) {
    const conditions = { user_id: userId };

    if (filters.status) {
      conditions.status = filters.status;
    }
    if (filters.period) {
      conditions.period = filters.period;
    }
    if (filters.category) {
      conditions.category = filters.category;
    }

    let queryOptions = {
      orderBy: { field: "start_date", ascending: false },
    };

    // 添加日期范围筛选
    if (filters.start_date || filters.end_date) {
      // 这里需要使用原生查询来处理日期范围
      let query = this.supabase
        .from("budgets")
        .select("*")
        .eq("user_id", userId);

      if (filters.status) query = query.eq("status", filters.status);
      if (filters.period) query = query.eq("period", filters.period);
      if (filters.category) query = query.eq("category", filters.category);
      if (filters.start_date) query = query.gte("end_date", filters.start_date);
      if (filters.end_date) query = query.lte("start_date", filters.end_date);

      const { data: budgets, error } = await query.order("start_date", {
        ascending: false,
      });

      if (error) {
        console.error("获取预算列表错误:", error);
        throw new Error("获取预算列表失败");
      }

      return budgets || [];
    }

    const budgets = await this.findWhere(
      conditions,
      [
        "id",
        "category",
        "amount",
        "period",
        "start_date",
        "end_date",
        "description",
        "status",
        "created_at",
        "updated_at",
      ],
      queryOptions
    );

    return budgets;
  }

  /**
   * 获取预算详情（包含使用情况）
   * @param {string} budgetId - 预算ID
   * @param {string} userId - 用户ID
   * @returns {Object} 预算详情
   */
  async getBudgetDetails(budgetId, userId) {
    // 获取预算基本信息
    const budget = await this.findById(budgetId);
    if (!budget || budget.user_id !== userId) {
      throw new NotFoundError("预算不存在");
    }

    // 获取该预算期间内的支出记录
    const { data: expenses, error } = await this.supabase
      .from("records")
      .select("amount, record_date")
      .eq("type", "expense")
      .eq("category", budget.category)
      .eq("user_id", userId)
      .gte("record_date", budget.start_date)
      .lte("record_date", budget.end_date);

    if (error) {
      console.error("获取预算支出记录错误:", error);
      throw new Error("获取预算详情失败");
    }

    // 计算使用情况
    let totalSpent = 0;
    let recordCount = 0;

    if (expenses) {
      expenses.forEach((expense) => {
        totalSpent += parseFloat(expense.amount);
        recordCount++;
      });
    }

    const remaining = budget.amount - totalSpent;
    const usagePercentage =
      budget.amount > 0 ? (totalSpent / budget.amount) * 100 : 0;

    // 更新预算状态
    let newStatus = budget.status;
    if (totalSpent > budget.amount) {
      newStatus = Budget.BUDGET_STATUS.EXCEEDED;
    } else if (new Date() > new Date(budget.end_date)) {
      newStatus = Budget.BUDGET_STATUS.INACTIVE;
    } else {
      newStatus = Budget.BUDGET_STATUS.ACTIVE;
    }

    // 如果状态发生变化，更新数据库
    if (newStatus !== budget.status) {
      await this.update(budgetId, { status: newStatus }, ["id"]);
    }

    return {
      ...budget,
      usage: {
        total_spent: totalSpent,
        remaining: remaining,
        usage_percentage: usagePercentage,
        record_count: recordCount,
        is_exceeded: totalSpent > budget.amount,
        days_remaining: Math.max(
          0,
          Math.ceil(
            (new Date(budget.end_date) - new Date()) / (1000 * 60 * 60 * 24)
          )
        ),
      },
      status: newStatus,
    };
  }

  /**
   * 更新预算
   * @param {string} budgetId - 预算ID
   * @param {string} userId - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新后的预算信息
   */
  async updateBudget(budgetId, userId, updateData) {
    // 验证预算是否存在且属于当前用户
    const existingBudget = await this.findById(budgetId);
    if (!existingBudget || existingBudget.user_id !== userId) {
      throw new NotFoundError("预算不存在");
    }

    // 数据验证
    const validation = this.validate(updateData, "update");
    if (!validation.isValid) {
      throw new ValidationError(validation.errors.join(", "));
    }

    // 如果更新了周期或开始日期，重新计算结束日期
    if (updateData.period || updateData.start_date) {
      const period = updateData.period || existingBudget.period;
      const startDate = updateData.start_date || existingBudget.start_date;
      updateData.end_date = this.calculateEndDate(startDate, period);
    }

    // 处理金额更新
    if (updateData.amount !== undefined) {
      updateData.amount = parseFloat(updateData.amount);
    }

    const updatedBudget = await this.update(budgetId, updateData, [
      "id",
      "category",
      "amount",
      "period",
      "start_date",
      "end_date",
      "description",
      "status",
      "updated_at",
    ]);

    return updatedBudget;
  }

  /**
   * 删除预算
   * @param {string} budgetId - 预算ID
   * @param {string} userId - 用户ID
   * @returns {boolean} 删除是否成功
   */
  async deleteBudget(budgetId, userId) {
    // 验证预算是否存在且属于当前用户
    const budget = await this.findById(budgetId);
    if (!budget || budget.user_id !== userId) {
      throw new NotFoundError("预算不存在");
    }

    return await this.delete(budgetId);
  }

  /**
   * 获取预算概览
   * @param {string} userId - 用户ID
   * @returns {Object} 预算概览信息
   */
  async getBudgetOverview(userId) {
    const currentDate = new Date().toISOString();

    // 获取当前活跃的预算
    const activeBudgets = await this.findWhere({
      user_id: userId,
      status: Budget.BUDGET_STATUS.ACTIVE,
    });

    const overview = {
      total_budgets: activeBudgets.length,
      total_budget_amount: 0,
      total_spent: 0,
      exceeded_count: 0,
      near_limit_count: 0, // 使用超过80%的预算数量
      budgets_summary: [],
    };

    for (const budget of activeBudgets) {
      const budgetDetails = await this.getBudgetDetails(budget.id, userId);

      overview.total_budget_amount += budget.amount;
      overview.total_spent += budgetDetails.usage.total_spent;

      if (budgetDetails.usage.is_exceeded) {
        overview.exceeded_count++;
      } else if (budgetDetails.usage.usage_percentage > 80) {
        overview.near_limit_count++;
      }

      overview.budgets_summary.push({
        id: budget.id,
        category: budget.category,
        amount: budget.amount,
        spent: budgetDetails.usage.total_spent,
        remaining: budgetDetails.usage.remaining,
        usage_percentage: budgetDetails.usage.usage_percentage,
        status: budgetDetails.status,
      });
    }

    return overview;
  }
}

// 导出单例实例
export default new Budget();
