import { config } from '../config/environment.js';

// 全局错误处理中间件
export const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // 默认错误响应
  let error = {
    error: '服务器内部错误',
    message: '服务器遇到了一个错误，请稍后重试',
    timestamp: new Date().toISOString()
  };

  let statusCode = 500;

  // 根据错误类型设置不同的响应
  switch (err.name) {
    case 'ValidationError':
      statusCode = 400;
      error.error = '数据验证错误';
      error.message = err.message;
      break;

    case 'UnauthorizedError':
    case 'JsonWebTokenError':
      statusCode = 401;
      error.error = '认证失败';
      error.message = '无效的访问令牌';
      break;

    case 'TokenExpiredError':
      statusCode = 401;
      error.error = '令牌已过期';
      error.message = '访问令牌已过期，请重新登录';
      break;

    case 'ForbiddenError':
      statusCode = 403;
      error.error = '访问被禁止';
      error.message = '您没有权限执行此操作';
      break;

    case 'NotFoundError':
      statusCode = 404;
      error.error = '资源未找到';
      error.message = err.message || '请求的资源不存在';
      break;

    case 'ConflictError':
      statusCode = 409;
      error.error = '数据冲突';
      error.message = err.message || '请求的操作与现有数据冲突';
      break;

    case 'TooManyRequestsError':
      statusCode = 429;
      error.error = '请求过于频繁';
      error.message = '请求次数过多，请稍后重试';
      break;

    default:
      // 检查是否是数据库错误
      if (err.code) {
        switch (err.code) {
          case '23505': // PostgreSQL unique violation
            statusCode = 409;
            error.error = '数据重复';
            error.message = '该数据已存在';
            break;
          case '23503': // PostgreSQL foreign key violation
            statusCode = 400;
            error.error = '数据关联错误';
            error.message = '关联的数据不存在';
            break;
          case '23502': // PostgreSQL not null violation
            statusCode = 400;
            error.error = '必填字段缺失';
            error.message = '请填写所有必填字段';
            break;
          default:
            if (config.nodeEnv === 'development') {
              error.message = err.message;
              error.stack = err.stack;
            }
        }
      } else if (config.nodeEnv === 'development') {
        error.message = err.message;
        error.stack = err.stack;
      }
  }

  // 如果响应已经发送，则不能再发送
  if (res.headersSent) {
    return next(err);
  }

  res.status(statusCode).json(error);
};

// 404 错误处理中间件
export const notFoundHandler = (req, res) => {
  res.status(404).json({
    error: '接口不存在',
    message: `路径 ${req.originalUrl} 未找到`,
    timestamp: new Date().toISOString()
  });
};

// 自定义错误类
export class AppError extends Error {
  constructor(message, statusCode = 500, name = 'AppError') {
    super(message);
    this.name = name;
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message) {
    super(message, 400, 'ValidationError');
  }
}

export class UnauthorizedError extends AppError {
  constructor(message = '认证失败') {
    super(message, 401, 'UnauthorizedError');
  }
}

export class ForbiddenError extends AppError {
  constructor(message = '访问被禁止') {
    super(message, 403, 'ForbiddenError');
  }
}

export class NotFoundError extends AppError {
  constructor(message = '资源未找到') {
    super(message, 404, 'NotFoundError');
  }
}

export class ConflictError extends AppError {
  constructor(message = '数据冲突') {
    super(message, 409, 'ConflictError');
  }
}

export class TooManyRequestsError extends AppError {
  constructor(message = '请求过于频繁') {
    super(message, 429, 'TooManyRequestsError');
  }
}
