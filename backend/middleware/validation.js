import { validationResult } from 'express-validator';

// 验证请求数据的中间件
export const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));

    return res.status(400).json({
      error: '数据验证失败',
      message: '请求数据不符合要求',
      details: formattedErrors
    });
  }
  
  next();
};

// 自定义验证函数
export const customValidators = {
  // 验证日期范围
  isValidDateRange: (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return start <= end;
  },

  // 验证金额格式
  isValidAmount: (amount) => {
    const num = parseFloat(amount);
    return !isNaN(num) && num > 0 && Number.isFinite(num);
  },

  // 验证账户类型
  isValidAccountType: (type) => {
    const validTypes = ['bank_card', 'alipay', 'wechat', 'cash', 'credit_card', 'other'];
    return validTypes.includes(type);
  },

  // 验证交易类型
  isValidTransactionType: (type) => {
    const validTypes = ['income', 'expense'];
    return validTypes.includes(type);
  },

  // 验证分页参数
  isValidPagination: (page, limit) => {
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    return pageNum > 0 && limitNum > 0 && limitNum <= 100;
  }
};

// 数据清理中间件
export const sanitizeInput = (req, res, next) => {
  // 清理字符串字段的前后空格
  const sanitizeObject = (obj) => {
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        obj[key] = obj[key].trim();
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeObject(obj[key]);
      }
    }
  };

  if (req.body && typeof req.body === 'object') {
    sanitizeObject(req.body);
  }

  if (req.query && typeof req.query === 'object') {
    sanitizeObject(req.query);
  }

  next();
};

// 文件上传验证中间件
export const validateFileUpload = (allowedTypes = [], maxSize = 5 * 1024 * 1024) => {
  return (req, res, next) => {
    if (!req.file) {
      return next();
    }

    // 检查文件类型
    if (allowedTypes.length > 0 && !allowedTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        error: '文件类型不支持',
        message: `只支持以下文件类型: ${allowedTypes.join(', ')}`
      });
    }

    // 检查文件大小
    if (req.file.size > maxSize) {
      return res.status(400).json({
        error: '文件过大',
        message: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`
      });
    }

    next();
  };
};
