import jwt from "jsonwebtoken";
import { config } from "../config/environment.js";
import { supabase } from "../config/database.js";

// JWT 认证中间件
export const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: "访问被拒绝",
        message: "缺少访问令牌",
      });
    }

    // 验证 JWT 令牌
    const decoded = jwt.verify(token, config.jwt.secret);

    // 验证用户是否仍然存在
    const { data: user, error } = await supabase
      .from("users")
      .select("id, email, name, is_active")
      .eq("id", decoded.userId)
      .single();

    if (error || !user) {
      return res.status(401).json({
        error: "认证失败",
        message: "用户不存在或已被禁用",
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        error: "账户已禁用",
        message: "您的账户已被禁用，请联系管理员",
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      email: user.email,
      name: user.name,
    };

    next();
  } catch (error) {
    if (error.name === "JsonWebTokenError") {
      return res.status(401).json({
        error: "认证失败",
        message: "无效的访问令牌",
      });
    }

    if (error.name === "TokenExpiredError") {
      return res.status(401).json({
        error: "令牌已过期",
        message: "访问令牌已过期，请重新登录",
      });
    }

    console.error("认证中间件错误:", error);
    return res.status(500).json({
      error: "服务器错误",
      message: "认证过程中发生错误",
    });
  }
};

// 可选认证中间件（用于某些可以匿名访问的接口）
export const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, config.jwt.secret);

    const { data: user, error } = await supabase
      .from("users")
      .select("id, email, name, is_active")
      .eq("id", decoded.userId)
      .single();

    if (!error && user && user.is_active) {
      req.user = {
        id: user.id,
        email: user.email,
        name: user.name,
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    req.user = null;
    next();
  }
};

// 生成 JWT 令牌
export const generateToken = (userId) => {
  return jwt.sign({ userId }, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
};

// 验证令牌（不通过中间件）
export const verifyToken = (token) => {
  try {
    return jwt.verify(token, config.jwt.secret);
  } catch (error) {
    return null;
  }
};
