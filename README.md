# 个人记账应用

基于 Nuxt.js + Express.js + Supabase 的现代化个人记账应用

## 项目概述

这是一个全栈的个人记账应用，旨在帮助用户轻松管理个人财务，追踪收支情况，并提供详细的统计分析功能。

### 主要功能

- 🔐 **用户管理** - 注册、登录、密码管理
- 💰 **收支记录** - 快速记录收入和支出
- 📊 **统计分析** - 月度、年度财务报表和趋势分析
- 🏷️ **分类管理** - 灵活的收支分类系统
- 💡 **预算管理** - 设置和跟踪预算目标
- 📱 **响应式设计** - 支持桌面和移动设备

## 技术栈

### 前端
- **框架**: Nuxt.js 3.17.5
- **UI 库**: Vue.js 3.5.17
- **语言**: TypeScript
- **路由**: Vue Router 4.5.1
- **构建工具**: Vite

### 后端
- **框架**: Express.js 4.18.3
- **运行环境**: Node.js (ES Modules)
- **数据库**: Supabase (PostgreSQL)
- **认证**: JWT
- **安全**: Helmet, CORS, Rate Limiting
- **验证**: Express Validator

## 项目结构

```
record/
├── README.md                 # 项目说明文档
├── requirements.md           # 需求分析文档
├── progress.md              # 开发进度追踪
├── backend/                 # 后端 Express.js 应用
│   ├── server.js            # 服务器入口
│   ├── config/              # 配置文件
│   ├── routes/              # API 路由
│   ├── controllers/         # 业务逻辑
│   ├── middleware/          # 中间件
│   ├── models/              # 数据模型
│   ├── database/            # 数据库脚本
│   └── scripts/             # 工具脚本
└── frontend/                # 前端 Nuxt.js 应用
    ├── app.vue              # 主应用组件
    ├── nuxt.config.ts       # Nuxt 配置
    ├── public/              # 静态资源
    └── [Nuxt.js 目录结构]   # 待创建
```

## 快速开始

### 环境要求

- Node.js 18+ 
- npm 或 yarn
- Supabase 账户

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 环境配置

1. 在 `backend/` 目录下创建 `.env` 文件：

```env
# Supabase 配置
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# JWT 配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3001
NODE_ENV=development
```

### 数据库初始化

```bash
cd backend
node database/init.js
```

### 启动开发服务器

```bash
# 启动后端服务器 (端口 3001)
cd backend
npm run dev

# 启动前端开发服务器 (端口 3000)
cd frontend
npm run dev
```

## API 接口

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新令牌

### 记录接口
- `GET /api/records` - 获取记录列表
- `POST /api/records` - 创建新记录
- `PUT /api/records/:id` - 更新记录
- `DELETE /api/records/:id` - 删除记录

### 统计接口
- `GET /api/statistics/summary` - 获取统计摘要
- `GET /api/statistics/monthly` - 月度统计
- `GET /api/statistics/yearly` - 年度统计

## 开发状态

### ✅ 已完成
- 项目初始化和基础架构
- 后端 API 框架搭建
- 数据模型设计
- 数据库表结构定义
- 认证和安全中间件

### 🔄 进行中
- Supabase 数据库配置
- API 接口实现

### ⏳ 待开始
- 前端页面开发
- 用户界面设计
- 前后端集成
- 测试和部署

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-username/record/issues)
- 邮箱: <EMAIL>

---

_最后更新时间: 2025-06-25_
